<template>
  <!-- <img
    alt="device-image"
    style="object-fit: contain;"
    :src="cover.url"
    class="filler "

  > -->
  <div class="device-image-wrapper">
    <!-- TODO:暂时不更新此功能 -->
    <!-- <div v-if="showShadow" :class="['shadow',shadowClass] " /> -->
    <img
      alt="device-image"
      style="object-fit: contain;"
      :src="cover.url"
      class="filler"
    >
  </div>
</template>

<script>
export default {
  name: 'DeviceImage',
  props: {
    cover: {
      type: Object,
      default: () => ({})
    },
    editing: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showShadow: false,
      shadowClass: ''
    }
  },
  watch: {
    cover: {
      handler(val) {
        // 判断温湿度传感器温度状态
        if (val.unit && val.unit == '℃' && val.iconPrefix == 'img_temphum_' && val.identifier.includes('Temp')) {
          const temp = parseFloat(val.value)
          if (temp > 26) {
            this.showShadow = true
            this.shadowClass = 'shadow-red'
          } else if (temp < 25) {
            this.showShadow = true
            this.shadowClass = 'shadow-blue'
          } else {
            this.showShadow = false
            this.shadowClass = ''
          }
          console.log('温度判断:', val)
        }
      },
      deep: true,
      immediate: true
    }
  }
}
</script>

<style lang="scss" scoped>
.filler {
  width: 100%;
  height: 100%;
  display: inline-block;
  position: absolute;
  z-index: 1;

  &.img {
    width: 100%;
  }
}

.device-image-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}
.shadow {
  position: absolute;
  width: 110%;
  height: 110%;
  left: -5%;
  top: -10%;
  right: 0;
  bottom: 0;
  z-index: 0;
  pointer-events: none;
  border-radius: 50%;
}

.shadow-red {
  background: #DE1717;
  filter: blur(4px);
}
.shadow-blue {
  background: linear-gradient( 180deg, #084CB9 0%, #073ABA 100%);
  filter: blur(4px);
}

</style>
